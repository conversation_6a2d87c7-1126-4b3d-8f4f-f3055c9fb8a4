{"hash": "8ecfd22e", "configHash": "7c0501f9", "lockfileHash": "e3b0c442", "browserHash": "edaccf81", "optimized": {"@elysiajs/eden": {"src": "../../../../node_modules/@elysiajs/eden/dist/index.mjs", "file": "@elysiajs_eden.js", "fileHash": "e8b7194a", "needsInterop": false}, "axios": {"src": "../../../../node_modules/axios/index.js", "file": "axios.js", "fileHash": "0be5150d", "needsInterop": false}, "pinia": {"src": "../../../../node_modules/pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "aa3763a9", "needsInterop": false}, "tailwindcss-intersect": {"src": "../../../../node_modules/tailwindcss-intersect/dist/index.esm.js", "file": "tailwindcss-intersect.js", "fileHash": "0e1a3bf5", "needsInterop": false}, "vue": {"src": "../../../../node_modules/vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "ae5756b6", "needsInterop": false}, "vue-i18n": {"src": "../../../../node_modules/vue-i18n/dist/vue-i18n.mjs", "file": "vue-i18n.js", "fileHash": "955554a5", "needsInterop": false}, "vue-router": {"src": "../../../../node_modules/vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "87e658c1", "needsInterop": false}}, "chunks": {"chunk-J256NH3B": {"file": "chunk-J256NH3B.js"}, "chunk-BCMC4SMG": {"file": "chunk-BCMC4SMG.js"}, "chunk-UVKRO5ER": {"file": "chunk-UVKRO5ER.js"}}}